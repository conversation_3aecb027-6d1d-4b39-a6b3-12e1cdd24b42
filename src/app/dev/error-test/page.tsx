'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { useTranslation } from '@/contexts/translation-context';
import {
	AlertTriangle,
	Bug,
	Zap,
	Database,
	Network,
	Shield,
	Clock,
	FileX,
	Server,
	Code,
} from 'lucide-react';
import { AppError, ValidationError, NetworkError, ServerError } from '@/lib/error-handling';
import { notFound } from 'next/navigation';

// Only allow access in development
if (process.env.NODE_ENV !== 'development') {
	notFound();
}

export default function ErrorTestPage() {
	const { t } = useTranslation();
	const [lastError, setLastError] = useState<string>('');

	// Test different types of errors
	const testErrors = [
		{
			name: 'General Error',
			icon: <Bug className="h-5 w-5" />,
			color: 'bg-red-500',
			action: () => {
				throw new Error('This is a test general error');
			},
		},
		{
			name: 'App Error',
			icon: <AlertTriangle className="h-5 w-5" />,
			color: 'bg-orange-500',
			action: () => {
				throw new AppError('This is a test application error', 'TEST_ERROR');
			},
		},
		{
			name: 'Validation Error',
			icon: <Shield className="h-5 w-5" />,
			color: 'bg-yellow-500',
			action: () => {
				throw new ValidationError('Invalid input data', {
					application: { component: 'test-form', action: 'validate' },
				});
			},
		},
		{
			name: 'Network Error',
			icon: <Network className="h-5 w-5" />,
			color: 'bg-blue-500',
			action: () => {
				throw new NetworkError('Failed to connect to server');
			},
		},
		{
			name: 'Server Error',
			icon: <Database className="h-5 w-5" />,
			color: 'bg-purple-500',
			action: () => {
				throw new ServerError('Database connection failed');
			},
		},
		{
			name: 'Timeout Error',
			icon: <Clock className="h-5 w-5" />,
			color: 'bg-indigo-500',
			action: () => {
				throw new Error('Request timeout after 30 seconds');
			},
		},
		{
			name: 'Not Found Error',
			icon: <FileX className="h-5 w-5" />,
			color: 'bg-gray-500',
			action: () => {
				throw new Error('Resource not found');
			},
		},
		{
			name: 'Server Error',
			icon: <Server className="h-5 w-5" />,
			color: 'bg-red-600',
			action: () => {
				throw new Error('Internal server error');
			},
		},
		{
			name: 'Syntax Error',
			icon: <Code className="h-5 w-5" />,
			color: 'bg-pink-500',
			action: () => {
				throw new SyntaxError('Unexpected token in JSON');
			},
		},
		{
			name: 'Promise Rejection',
			icon: <Zap className="h-5 w-5" />,
			color: 'bg-green-500',
			action: async () => {
				await Promise.reject(new Error('Unhandled promise rejection'));
			},
		},
	];

	const handleErrorTest = async (errorTest: (typeof testErrors)[0]) => {
		try {
			setLastError(`Testing: ${errorTest.name}`);
			await errorTest.action();
		} catch (error) {
			setLastError(
				`Caught: ${errorTest.name} - ${
					error instanceof Error ? error.message : 'Unknown error'
				}`
			);
			// Re-throw to trigger error boundary
			throw error;
		}
	};

	const testAsyncError = () => {
		setTimeout(() => {
			throw new Error('Async error after 1 second');
		}, 1000);
		setLastError('Async error scheduled for 1 second...');
	};

	const testFetchError = async () => {
		try {
			setLastError('Testing fetch error...');
			const response = await fetch('/api/non-existent-endpoint');
			if (!response.ok) {
				throw new NetworkError(`HTTP ${response.status}: ${response.statusText}`);
			}
		} catch (error) {
			setLastError(
				`Fetch error: ${error instanceof Error ? error.message : 'Unknown error'}`
			);
			throw error;
		}
	};

	const testComponentError = () => {
		// This will cause a React component error
		setLastError('Testing component error...');
		throw new Error('Component render error');
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-6">
			<div className="max-w-6xl mx-auto space-y-6">
				{/* Header */}
				<Card className="border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950">
					<CardHeader>
						<CardTitle className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200">
							<AlertTriangle className="h-6 w-6" />
							Error Testing Page (Development Only)
						</CardTitle>
					</CardHeader>
					<CardContent>
						<p className="text-yellow-700 dark:text-yellow-300">
							This page is only accessible in development mode. Use it to test
							different error scenarios and verify that error boundaries and error
							handling work correctly.
						</p>
						{lastError && (
							<div className="mt-4 p-3 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
								<p className="text-sm font-mono text-yellow-800 dark:text-yellow-200">
									Last action: {lastError}
								</p>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Error Test Grid */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{testErrors.map((errorTest, index) => (
						<Card key={index} className="hover:shadow-lg transition-shadow">
							<CardContent className="p-4">
								<div className="flex items-center gap-3 mb-3">
									<div className={`p-2 rounded-lg ${errorTest.color} text-white`}>
										{errorTest.icon}
									</div>
									<h3 className="font-semibold text-gray-900 dark:text-white">
										{errorTest.name}
									</h3>
								</div>
								<Button
									onClick={() => handleErrorTest(errorTest)}
									variant="outline"
									className="w-full"
								>
									Trigger Error
								</Button>
							</CardContent>
						</Card>
					))}
				</div>

				{/* Special Test Cases */}
				<Card>
					<CardHeader>
						<CardTitle>Special Test Cases</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<Button
								onClick={testAsyncError}
								variant="destructive"
								className="flex items-center gap-2"
							>
								<Clock className="h-4 w-4" />
								Async Error (1s delay)
							</Button>

							<Button
								onClick={testFetchError}
								variant="destructive"
								className="flex items-center gap-2"
							>
								<Network className="h-4 w-4" />
								Fetch Error
							</Button>

							<Button
								onClick={testComponentError}
								variant="destructive"
								className="flex items-center gap-2"
							>
								<Code className="h-4 w-4" />
								Component Error
							</Button>
						</div>
					</CardContent>
				</Card>

				{/* Instructions */}
				<Card>
					<CardHeader>
						<CardTitle>Testing Instructions</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="prose dark:prose-invert max-w-none">
							<h4>How to test:</h4>
							<ol>
								<li>Click any error button to trigger that specific error type</li>
								<li>
									Observe how the error boundary catches and displays the error
								</li>
								<li>Check the browser console for error logs</li>
								<li>Verify that error reporting works correctly</li>
								<li>Test error recovery by clicking "Try again" buttons</li>
							</ol>

							<h4>What to verify:</h4>
							<ul>
								<li>Error boundaries catch and display errors properly</li>
								<li>Error pages show appropriate messages and actions</li>
								<li>Error logging and reporting functions work</li>
								<li>Users can recover from errors gracefully</li>
								<li>Different error types are handled appropriately</li>
							</ul>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
