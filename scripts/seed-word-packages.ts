#!/usr/bin/env tsx

import { PrismaClient, Language, Difficulty } from '@prisma/client';
import { config } from 'dotenv';

// Load environment variables
config();

const prisma = new PrismaClient();

interface WordPackageData {
	name: string;
	description: string;
	source_language: Language;
	target_language: Language;
	difficulty: Difficulty;
	category: string;
	tags: string[];
	words: Array<{
		term: string;
		language: Language;
	}>;
}

const SAMPLE_WORD_PACKAGES: WordPackageData[] = [
	// ===== ENGLISH PACKAGES (Learning English words with Vietnamese explanations) =====
	{
		name: 'Business English Essentials',
		description:
			'Essential vocabulary for professional business communication and workplace interactions',
		source_language: Language.EN, // Learning English words
		target_language: Language.VI, // Explained in Vietnamese
		difficulty: Difficulty.INTERMEDIATE,
		category: 'Business',
		tags: ['professional', 'workplace', 'communication', 'corporate'],
		words: [
			{ term: 'negotiate', language: Language.EN },
			{ term: 'proposal', language: Language.EN },
			{ term: 'deadline', language: Language.EN },
			{ term: 'revenue', language: Language.EN },
			{ term: 'stakeholder', language: Language.EN },
			{ term: 'strategy', language: Language.EN },
			{ term: 'budget', language: Language.EN },
			{ term: 'meeting', language: Language.EN },
			{ term: 'presentation', language: Language.EN },
			{ term: 'collaboration', language: Language.EN },
			{ term: 'quarterly', language: Language.EN },
			{ term: 'profit', language: Language.EN },
			{ term: 'investment', language: Language.EN },
			{ term: 'contract', language: Language.EN },
			{ term: 'merger', language: Language.EN },
		],
	},
	{
		name: 'Travel & Tourism Vocabulary',
		description:
			'Essential words and phrases for traveling, booking accommodations, and navigating new places',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.BEGINNER,
		category: 'Travel',
		tags: ['vacation', 'tourism', 'transportation', 'accommodation'],
		words: [
			{ term: 'airport', language: Language.EN },
			{ term: 'passport', language: Language.EN },
			{ term: 'luggage', language: Language.EN },
			{ term: 'hotel', language: Language.EN },
			{ term: 'reservation', language: Language.EN },
			{ term: 'ticket', language: Language.EN },
			{ term: 'destination', language: Language.EN },
			{ term: 'journey', language: Language.EN },
			{ term: 'tourist', language: Language.EN },
			{ term: 'vacation', language: Language.EN },
			{ term: 'itinerary', language: Language.EN },
			{ term: 'boarding pass', language: Language.EN },
			{ term: 'customs', language: Language.EN },
			{ term: 'currency', language: Language.EN },
			{ term: 'guidebook', language: Language.EN },
		],
	},
	{
		name: 'Academic English Advanced',
		description:
			'Sophisticated vocabulary for academic writing, research, and scholarly communication',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.ADVANCED,
		category: 'Academic',
		tags: ['university', 'research', 'writing', 'scholarly'],
		words: [
			{ term: 'hypothesis', language: Language.EN },
			{ term: 'methodology', language: Language.EN },
			{ term: 'analysis', language: Language.EN },
			{ term: 'conclusion', language: Language.EN },
			{ term: 'bibliography', language: Language.EN },
			{ term: 'dissertation', language: Language.EN },
			{ term: 'peer-review', language: Language.EN },
			{ term: 'citation', language: Language.EN },
			{ term: 'abstract', language: Language.EN },
			{ term: 'empirical', language: Language.EN },
			{ term: 'theoretical', language: Language.EN },
			{ term: 'paradigm', language: Language.EN },
			{ term: 'correlation', language: Language.EN },
			{ term: 'synthesis', language: Language.EN },
			{ term: 'qualitative', language: Language.EN },
		],
	},
	{
		name: 'Technology & Digital World',
		description:
			'Modern technology vocabulary covering software, hardware, internet, and digital innovations',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.INTERMEDIATE,
		category: 'Technology',
		tags: ['digital', 'software', 'internet', 'innovation'],
		words: [
			{ term: 'algorithm', language: Language.EN },
			{ term: 'database', language: Language.EN },
			{ term: 'interface', language: Language.EN },
			{ term: 'software', language: Language.EN },
			{ term: 'hardware', language: Language.EN },
			{ term: 'network', language: Language.EN },
			{ term: 'cybersecurity', language: Language.EN },
			{ term: 'artificial intelligence', language: Language.EN },
			{ term: 'cloud computing', language: Language.EN },
			{ term: 'blockchain', language: Language.EN },
			{ term: 'machine learning', language: Language.EN },
			{ term: 'encryption', language: Language.EN },
			{ term: 'bandwidth', language: Language.EN },
			{ term: 'debugging', language: Language.EN },
			{ term: 'optimization', language: Language.EN },
		],
	},
	{
		name: 'Medical & Health Terms',
		description: 'Essential medical vocabulary for healthcare professionals and patients',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.ADVANCED,
		category: 'Medical',
		tags: ['healthcare', 'medicine', 'anatomy', 'treatment'],
		words: [
			{ term: 'diagnosis', language: Language.EN },
			{ term: 'symptom', language: Language.EN },
			{ term: 'treatment', language: Language.EN },
			{ term: 'prescription', language: Language.EN },
			{ term: 'surgery', language: Language.EN },
			{ term: 'therapy', language: Language.EN },
			{ term: 'vaccination', language: Language.EN },
			{ term: 'infection', language: Language.EN },
			{ term: 'recovery', language: Language.EN },
			{ term: 'chronic', language: Language.EN },
			{ term: 'acute', language: Language.EN },
			{ term: 'rehabilitation', language: Language.EN },
			{ term: 'consultation', language: Language.EN },
			{ term: 'examination', language: Language.EN },
			{ term: 'medication', language: Language.EN },
		],
	},
	{
		name: 'Food & Cooking Essentials',
		description: 'Culinary vocabulary for cooking, dining, and food preparation',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.BEGINNER,
		category: 'Food',
		tags: ['cooking', 'cuisine', 'ingredients', 'dining'],
		words: [
			{ term: 'ingredient', language: Language.EN },
			{ term: 'recipe', language: Language.EN },
			{ term: 'seasoning', language: Language.EN },
			{ term: 'marinade', language: Language.EN },
			{ term: 'appetizer', language: Language.EN },
			{ term: 'dessert', language: Language.EN },
			{ term: 'beverage', language: Language.EN },
			{ term: 'cuisine', language: Language.EN },
			{ term: 'portion', language: Language.EN },
			{ term: 'nutrition', language: Language.EN },
			{ term: 'organic', language: Language.EN },
			{ term: 'vegetarian', language: Language.EN },
			{ term: 'gluten-free', language: Language.EN },
			{ term: 'fermentation', language: Language.EN },
			{ term: 'garnish', language: Language.EN },
		],
	},
	// ===== VIETNAMESE PACKAGES (Learning Vietnamese words with English explanations) =====
	{
		name: 'Từ vựng Kinh doanh Cơ bản',
		description:
			'Từ vựng cần thiết cho giao tiếp kinh doanh và môi trường công sở chuyên nghiệp',
		source_language: Language.VI, // Learning Vietnamese words
		target_language: Language.EN, // Explained in English
		difficulty: Difficulty.INTERMEDIATE,
		category: 'Kinh doanh',
		tags: ['chuyên nghiệp', 'công việc', 'giao tiếp', 'doanh nghiệp'],
		words: [
			{ term: 'đàm phán', language: Language.VI },
			{ term: 'đề xuất', language: Language.VI },
			{ term: 'thời hạn', language: Language.VI },
			{ term: 'doanh thu', language: Language.VI },
			{ term: 'bên liên quan', language: Language.VI },
			{ term: 'chiến lược', language: Language.VI },
			{ term: 'ngân sách', language: Language.VI },
			{ term: 'cuộc họp', language: Language.VI },
			{ term: 'thuyết trình', language: Language.VI },
			{ term: 'hợp tác', language: Language.VI },
			{ term: 'lợi nhuận', language: Language.VI },
			{ term: 'đầu tư', language: Language.VI },
			{ term: 'hợp đồng', language: Language.VI },
			{ term: 'sáp nhập', language: Language.VI },
			{ term: 'quản lý', language: Language.VI },
		],
	},
	{
		name: 'Từ vựng Du lịch & Khám phá',
		description: 'Từ vựng thông dụng cho việc du lịch, đặt phòng và khám phá địa điểm mới',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.BEGINNER,
		category: 'Du lịch',
		tags: ['nghỉ dưỡng', 'du lịch', 'giao thông', 'khám phá'],
		words: [
			{ term: 'sân bay', language: Language.VI },
			{ term: 'hộ chiếu', language: Language.VI },
			{ term: 'hành lý', language: Language.VI },
			{ term: 'khách sạn', language: Language.VI },
			{ term: 'đặt chỗ', language: Language.VI },
			{ term: 'vé máy bay', language: Language.VI },
			{ term: 'điểm đến', language: Language.VI },
			{ term: 'hành trình', language: Language.VI },
			{ term: 'khách du lịch', language: Language.VI },
			{ term: 'kỳ nghỉ', language: Language.VI },
			{ term: 'lịch trình', language: Language.VI },
			{ term: 'thẻ lên máy bay', language: Language.VI },
			{ term: 'hải quan', language: Language.VI },
			{ term: 'tiền tệ', language: Language.VI },
			{ term: 'sách hướng dẫn', language: Language.VI },
		],
	},
	{
		name: 'Từ vựng Công nghệ Thông tin',
		description: 'Từ vựng về công nghệ hiện đại, phần mềm, phần cứng và đổi mới số',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.INTERMEDIATE,
		category: 'Công nghệ',
		tags: ['số hóa', 'phần mềm', 'internet', 'đổi mới'],
		words: [
			{ term: 'thuật toán', language: Language.VI },
			{ term: 'cơ sở dữ liệu', language: Language.VI },
			{ term: 'giao diện', language: Language.VI },
			{ term: 'phần mềm', language: Language.VI },
			{ term: 'phần cứng', language: Language.VI },
			{ term: 'mạng lưới', language: Language.VI },
			{ term: 'an ninh mạng', language: Language.VI },
			{ term: 'trí tuệ nhân tạo', language: Language.VI },
			{ term: 'điện toán đám mây', language: Language.VI },
			{ term: 'chuỗi khối', language: Language.VI },
			{ term: 'học máy', language: Language.VI },
			{ term: 'mã hóa', language: Language.VI },
			{ term: 'băng thông', language: Language.VI },
			{ term: 'gỡ lỗi', language: Language.VI },
			{ term: 'tối ưu hóa', language: Language.VI },
		],
	},
	{
		name: 'Từ vựng Y tế & Sức khỏe',
		description: 'Từ vựng y tế cần thiết cho nhân viên y tế và bệnh nhân',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.ADVANCED,
		category: 'Y tế',
		tags: ['chăm sóc sức khỏe', 'y học', 'điều trị', 'bệnh viện'],
		words: [
			{ term: 'chẩn đoán', language: Language.VI },
			{ term: 'triệu chứng', language: Language.VI },
			{ term: 'điều trị', language: Language.VI },
			{ term: 'đơn thuốc', language: Language.VI },
			{ term: 'phẫu thuật', language: Language.VI },
			{ term: 'liệu pháp', language: Language.VI },
			{ term: 'tiêm chủng', language: Language.VI },
			{ term: 'nhiễm trùng', language: Language.VI },
			{ term: 'hồi phục', language: Language.VI },
			{ term: 'mãn tính', language: Language.VI },
			{ term: 'cấp tính', language: Language.VI },
			{ term: 'phục hồi chức năng', language: Language.VI },
			{ term: 'tư vấn', language: Language.VI },
			{ term: 'khám bệnh', language: Language.VI },
			{ term: 'thuốc men', language: Language.VI },
		],
	},
	{
		name: 'Từ vựng Ẩm thực Việt Nam',
		description: 'Từ vựng về nấu ăn, ẩm thực và chuẩn bị món ăn truyền thống Việt Nam',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.BEGINNER,
		category: 'Ẩm thực',
		tags: ['nấu ăn', 'món ăn', 'nguyên liệu', 'ẩm thực'],
		words: [
			{ term: 'nguyên liệu', language: Language.VI },
			{ term: 'công thức', language: Language.VI },
			{ term: 'gia vị', language: Language.VI },
			{ term: 'ướp', language: Language.VI },
			{ term: 'món khai vị', language: Language.VI },
			{ term: 'món tráng miệng', language: Language.VI },
			{ term: 'đồ uống', language: Language.VI },
			{ term: 'ẩm thực', language: Language.VI },
			{ term: 'khẩu phần', language: Language.VI },
			{ term: 'dinh dưỡng', language: Language.VI },
			{ term: 'hữu cơ', language: Language.VI },
			{ term: 'chay', language: Language.VI },
			{ term: 'không gluten', language: Language.VI },
			{ term: 'lên men', language: Language.VI },
			{ term: 'trang trí', language: Language.VI },
		],
	},
	{
		name: 'Từ vựng Giáo dục & Học tập',
		description: 'Từ vựng về giáo dục, học tập và môi trường trường học',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.INTERMEDIATE,
		category: 'Giáo dục',
		tags: ['học tập', 'trường học', 'giáo dục', 'kiến thức'],
		words: [
			{ term: 'giáo dục', language: Language.VI },
			{ term: 'học sinh', language: Language.VI },
			{ term: 'giáo viên', language: Language.VI },
			{ term: 'bài học', language: Language.VI },
			{ term: 'kiểm tra', language: Language.VI },
			{ term: 'thi cử', language: Language.VI },
			{ term: 'điểm số', language: Language.VI },
			{ term: 'bằng cấp', language: Language.VI },
			{ term: 'chương trình học', language: Language.VI },
			{ term: 'nghiên cứu', language: Language.VI },
			{ term: 'thí nghiệm', language: Language.VI },
			{ term: 'thư viện', language: Language.VI },
			{ term: 'học bổng', language: Language.VI },
			{ term: 'tốt nghiệp', language: Language.VI },
			{ term: 'kiến thức', language: Language.VI },
		],
	},
];

async function createWordPackage(packageData: WordPackageData): Promise<void> {
	const { words, ...packageInfo } = packageData;

	try {
		// Check if package already exists
		const existingPackage = await prisma.wordPackage.findFirst({
			where: {
				name: packageData.name,
				source_language: packageData.source_language,
				target_language: packageData.target_language,
			},
		});

		if (existingPackage) {
			console.log(`📦 Word package '${packageData.name}' already exists. Skipping...`);
			return;
		}

		// Create the word package
		const wordPackage = await prisma.wordPackage.create({
			data: {
				...packageInfo,
				word_count: words.length,
				is_active: true,
			},
		});

		// Add words to the package
		const wordPackageWords = words.map((word) => ({
			word_package_id: wordPackage.id,
			term: word.term,
			language: word.language,
		}));

		await prisma.wordPackageWord.createMany({
			data: wordPackageWords,
		});

		console.log(`✅ Created word package: ${packageData.name} (${words.length} words)`);
	} catch (error) {
		console.error(`❌ Failed to create word package '${packageData.name}':`, error);
	}
}

async function seedWordPackages(): Promise<void> {
	console.log('🌱 Starting word package seeding...');

	try {
		// Connect to database
		await prisma.$connect();
		console.log('📦 Connected to database');

		// Create word packages
		for (const packageData of SAMPLE_WORD_PACKAGES) {
			await createWordPackage(packageData);
		}

		console.log('\n🎉 Word package seeding completed successfully!');
		console.log(`📊 Created ${SAMPLE_WORD_PACKAGES.length} word packages`);

		// Show summary
		const packageStats = await prisma.wordPackage.groupBy({
			by: ['source_language', 'target_language', 'difficulty'],
			_count: {
				_all: true,
			},
		});

		console.log('\n📋 Package Summary:');
		console.log('==================');
		packageStats.forEach((stat) => {
			console.log(
				`${stat.source_language} → ${stat.target_language} - ${stat.difficulty}: ${stat._count._all} packages`
			);
		});
	} catch (error) {
		console.error('❌ Word package seeding failed:', error);
		throw error;
	} finally {
		await prisma.$disconnect();
	}
}

// Main execution
async function main(): Promise<void> {
	await seedWordPackages();
}

// Run the script
if (require.main === module) {
	main().catch((error) => {
		console.error('Script execution failed:', error);
		process.exit(1);
	});
}

export { seedWordPackages };
